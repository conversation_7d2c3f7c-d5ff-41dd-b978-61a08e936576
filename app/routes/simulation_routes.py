from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional

from database.base import get_db_dependency
from database.crud.simulation_data import simulation_data
from schemas.simulation import (
    Simulation3DSubmitRequest,
    SimulationDataResponse,
)
from routes import logger


router = APIRouter(
    prefix='/simulation',
    tags=['仿真管理'],
    responses={404: {'description': '资源未找到'}, 500: {'description': '服务器内部错误'}},
)


@router.post(
    '/3d-submit/{erp}',
    response_model=SimulationDataResponse,
    summary='提交仿真三维搭建数据',
    description='为指定ERP提交仿真三维搭建数据，如果已存在则更新，否则创建新记录',
)
async def submit_simulation_3d_data(
    erp: str,
    request: Simulation3DSubmitRequest,
    db: AsyncSession = Depends(get_db_dependency),
):
    """提交仿真三维搭建数据"""
    try:
        # 准备要更新/插入的数据
        simulation_3d_data = {
            'simulation_3d_responsible': request.simulation_3d_responsible,
            'simulation_3d_start_time': request.simulation_3d_start_time,
            'simulation_3d_end_time': request.simulation_3d_end_time,
        }
        
        # 执行upsert操作
        result = await simulation_data.upsert_simulation_3d_data(
            db, erp, simulation_3d_data
        )
        
        await db.commit()
        
        logger.info(f'成功提交ERP {erp} 的仿真三维搭建数据')
        return result
        
    except ValueError as e:
        logger.error(f'提交仿真三维搭建数据失败: {str(e)}')
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f'提交仿真三维搭建数据失败: {str(e)}')
        await db.rollback()
        raise HTTPException(status_code=500, detail=f'提交仿真三维搭建数据失败: {str(e)}')


@router.get(
    '/data/{erp}',
    response_model=Optional[SimulationDataResponse],
    summary='获取仿真数据',
    description='根据ERP获取仿真数据',
)
async def get_simulation_data_by_erp(
    erp: str,
    db: AsyncSession = Depends(get_db_dependency),
):
    """根据ERP获取仿真数据"""
    try:
        result = await simulation_data.get_simulation_data_by_erp(db, erp)
        return result
    except Exception as e:
        logger.error(f'获取仿真数据失败: {str(e)}')
        raise HTTPException(status_code=500, detail=f'获取仿真数据失败: {str(e)}')
