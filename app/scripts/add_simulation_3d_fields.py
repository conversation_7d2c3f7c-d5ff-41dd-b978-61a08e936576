"""
添加仿真三维搭建相关字段到仿真数据表
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from database.base import engine
from logger import Logger

logger = Logger('add_simulation_3d_fields')


async def add_simulation_3d_fields():
    """添加仿真三维搭建相关字段"""
    
    # 要添加的字段
    fields_to_add = [
        {
            'name': 'simulation_3d_responsible',
            'type': 'VARCHAR(100)',
            'comment': '仿真搭建负责人'
        },
        {
            'name': 'simulation_3d_start_time',
            'type': 'DATETIME',
            'comment': '搭建开始时间'
        },
        {
            'name': 'simulation_3d_end_time',
            'type': 'DATETIME',
            'comment': '搭建结束时间'
        }
    ]
    
    async with engine.begin() as conn:
        # 检查表是否存在
        check_table_sql = """
        SELECT COUNT(*) as count 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE() 
        AND table_name = 'workflow_simulation_data'
        """
        
        result = await conn.execute(text(check_table_sql))
        table_exists = result.fetchone()[0] > 0
        
        if not table_exists:
            logger.warning("表 workflow_simulation_data 不存在，跳过字段添加")
            return
        
        # 检查并添加每个字段
        for field in fields_to_add:
            # 检查字段是否已存在
            check_field_sql = f"""
            SELECT COUNT(*) as count 
            FROM information_schema.columns 
            WHERE table_schema = DATABASE() 
            AND table_name = 'workflow_simulation_data' 
            AND column_name = '{field['name']}'
            """
            
            result = await conn.execute(text(check_field_sql))
            field_exists = result.fetchone()[0] > 0
            
            if field_exists:
                logger.info(f"字段 {field['name']} 已存在，跳过")
                continue
            
            # 添加字段
            add_field_sql = f"""
            ALTER TABLE workflow_simulation_data 
            ADD COLUMN {field['name']} {field['type']} 
            COMMENT '{field['comment']}'
            """
            
            try:
                await conn.execute(text(add_field_sql))
                logger.info(f"成功添加字段: {field['name']}")
            except Exception as e:
                logger.error(f"添加字段 {field['name']} 失败: {str(e)}")
                raise
        
        logger.info("所有仿真三维搭建字段添加完成")


async def main():
    """主函数"""
    try:
        await add_simulation_3d_fields()
        logger.info("数据库字段添加完成")
    except Exception as e:
        logger.error(f"数据库字段添加失败: {str(e)}")
        raise
    finally:
        await engine.dispose()


if __name__ == "__main__":
    asyncio.run(main())
