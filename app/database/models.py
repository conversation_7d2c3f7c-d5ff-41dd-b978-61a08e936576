from sqlalchemy import (
    Column,
    Integer,
    String,
    Text,
    ForeignKey,
    Enum,
    DateTime,
    Boolean,
    UniqueConstraint,
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database.base import Base
import enum
from schemas.base import DeviceType


class WorkflowStatus(enum.Enum):
    active = 'active'
    completed = 'completed'
    suspended = 'suspended'
    terminated = 'terminated'


class NodeStatus(enum.Enum):
    pending = 'pending'
    active = 'active'
    completed = 'completed'
    error = 'error'
    overdue = 'overdue'


class NodeType(enum.Enum):
    start = 'start'
    end = 'end'
    process = 'process'
    decision = 'decision'
    subprocess = 'subprocess'
    polling = 'polling'  # 添加polling类型


class StatusUpdateMode(enum.Enum):
    manual = 'manual'  # 手动更新状态
    polling = 'polling'  # 通过轮询更新状态
    callback = 'callback'  # 通过回调更新状态


class EdgeCondition(enum.Enum):
    yes = 'yes'
    no = 'no'
    default = 'default'


class InputLogicType(enum.Enum):
    """定义节点多输入时的逻辑关系"""

    AND = 'and'  # 所有输入节点都完成才能继续
    OR = 'or'  # 任一输入节点完成即可继续


class WorkflowDefinition(Base):
    __tablename__ = 'workflow_definitions'

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    version = Column(Integer, nullable=False, default=1)
    is_latest = Column(Boolean, nullable=False, default=True)
    is_subprocess = Column(Boolean, nullable=False, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    nodes = relationship(
        'NodeDefinition',
        back_populates='workflow',
        foreign_keys='NodeDefinition.workflow_definition_id',
    )
    edges = relationship('EdgeDefinition', back_populates='workflow')
    instances = relationship('WorkflowInstance', back_populates='definition')

    __table_args__ = (
        UniqueConstraint('name', 'version', name='uq_workflow_name_version'),
    )


class WorkflowInstance(Base):
    __tablename__ = 'workflow_instances'

    id = Column(Integer, primary_key=True, index=True)
    workflow_definition_id = Column(
        Integer,
        ForeignKey('workflow_definitions.id'),
        nullable=False,
        index=True,
    )
    status = Column(Enum(WorkflowStatus), nullable=False)
    start_time = Column(DateTime, nullable=False)
    end_time = Column(DateTime)
    machine_type = Column(String(50), nullable=False)
    created_by = Column(String(255))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    project_id = Column(String(50))
    device_type = Column(
        Enum(DeviceType), nullable=False, default=DeviceType.main_machine
    )

    definition = relationship('WorkflowDefinition', back_populates='instances')
    nodes = relationship(
        'NodeInstance',
        back_populates='workflow',
        cascade='all, delete-orphan',
        foreign_keys='NodeInstance.workflow_instance_id',
    )
    edges = relationship(
        'EdgeInstance', back_populates='workflow', cascade='all, delete-orphan'
    )
    variables = relationship(
        'InstanceVariable',
        back_populates='workflow',
        cascade='all, delete-orphan',
    )


class NodeDefinition(Base):
    __tablename__ = 'workflow_node_definitions'

    id = Column(Integer, primary_key=True, index=True)
    workflow_definition_id = Column(
        Integer, ForeignKey('workflow_definitions.id'), nullable=False
    )
    name = Column(String(255), nullable=False)
    type = Column(Enum(NodeType), nullable=False)
    color = Column(String(20))
    subprocess_id = Column(Integer, ForeignKey('workflow_definitions.id'))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    task_url = Column(String(255))
    need_approval = Column(Boolean)

    # 新增输入逻辑类型字段
    input_logic = Column(
        Enum(InputLogicType),
        nullable=False,
        default=InputLogicType.AND,
        comment='多输入节点的逻辑关系类型',
    )

    # 新增状态更新相关字段
    status_update_mode = Column(
        Enum(StatusUpdateMode), nullable=False, default=StatusUpdateMode.manual
    )
    status_query = Column(Text)  # 存储状态查询SQL

    workflow = relationship(
        'WorkflowDefinition',
        back_populates='nodes',
        foreign_keys=[workflow_definition_id],
    )
    subprocess = relationship(
        'WorkflowDefinition', foreign_keys=[subprocess_id]
    )
    instances = relationship('NodeInstance', back_populates='definition')
    machine_durations = relationship(
        'MachineTypeDuration',
        back_populates='node_definition',
        cascade='all, delete-orphan',
    )


class EdgeDefinition(Base):
    __tablename__ = 'workflow_edge_definitions'

    id = Column(Integer, primary_key=True, index=True)
    workflow_definition_id = Column(
        Integer, ForeignKey('workflow_definitions.id'), nullable=False
    )
    from_node_id = Column(Integer, ForeignKey('workflow_node_definitions.id'))
    to_node_id = Column(Integer, ForeignKey('workflow_node_definitions.id'))
    condition = Column(Enum(EdgeCondition))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    workflow = relationship('WorkflowDefinition', back_populates='edges')
    from_node = relationship('NodeDefinition', foreign_keys=[from_node_id])
    to_node = relationship('NodeDefinition', foreign_keys=[to_node_id])
    instances = relationship('EdgeInstance', back_populates='definition')


class EdgeInstance(Base):
    __tablename__ = 'workflow_edge_instances'

    id = Column(Integer, primary_key=True, index=True)
    workflow_instance_id = Column(
        Integer,
        ForeignKey('workflow_instances.id'),
        nullable=False,
        index=True,
    )
    edge_definition_id = Column(
        Integer,
        ForeignKey('workflow_edge_definitions.id'),
        nullable=False,
        index=True,
    )
    from_node_instance_id = Column(
        Integer, ForeignKey('workflow_node_instances.id'), index=True
    )
    to_node_instance_id = Column(
        Integer, ForeignKey('workflow_node_instances.id'), index=True
    )
    transition_time = Column(DateTime)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    workflow = relationship('WorkflowInstance', back_populates='edges')
    definition = relationship('EdgeDefinition', back_populates='instances')
    from_node_instance = relationship(
        'NodeInstance',
        foreign_keys=[from_node_instance_id],
        back_populates='outgoing_edges',
    )
    to_node_instance = relationship(
        'NodeInstance',
        foreign_keys=[to_node_instance_id],
        back_populates='incoming_edges',
    )


class NodeInstance(Base):
    __tablename__ = 'workflow_node_instances'

    id = Column(Integer, primary_key=True, index=True)
    workflow_instance_id = Column(
        Integer,
        ForeignKey('workflow_instances.id'),
        nullable=False,
        index=True,
    )
    node_definition_id = Column(
        Integer,
        ForeignKey('workflow_node_definitions.id'),
        nullable=False,
        index=True,
    )
    subprocess_instance_id = Column(
        Integer, ForeignKey('workflow_instances.id'), index=True
    )
    status = Column(Enum(NodeStatus), nullable=False)
    start_time = Column(DateTime)
    end_time = Column(DateTime)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    assigned_to = Column(String(255))
    notification_frequency = Column(
        String(255)
    )  # -1 不通知 0 通知1次 60 一小时 30 30分钟
    last_notification_time = Column(String(255))
    actual_duration = Column(Integer)

    workflow = relationship(
        'WorkflowInstance',
        foreign_keys=[workflow_instance_id],
        back_populates='nodes',
    )
    definition = relationship('NodeDefinition', back_populates='instances')
    subprocess_instance = relationship(
        'WorkflowInstance', foreign_keys=[subprocess_instance_id]
    )
    variables = relationship(
        'InstanceVariable', back_populates='node', cascade='all, delete-orphan'
    )
    outgoing_edges = relationship(
        'EdgeInstance',
        foreign_keys=[EdgeInstance.from_node_instance_id],
        back_populates='from_node_instance',
    )
    incoming_edges = relationship(
        'EdgeInstance',
        foreign_keys=[EdgeInstance.to_node_instance_id],
        back_populates='to_node_instance',
    )


class InstanceVariable(Base):
    __tablename__ = 'workflow_instance_variables'

    id = Column(Integer, primary_key=True, index=True)
    workflow_instance_id = Column(
        Integer,
        ForeignKey('workflow_instances.id'),
        nullable=False,
        index=True,
    )
    node_instance_id = Column(
        Integer, ForeignKey('workflow_node_instances.id'), index=True
    )
    name = Column(String(255), nullable=False)
    value = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    workflow = relationship('WorkflowInstance', back_populates='variables')
    node = relationship('NodeInstance', back_populates='variables')


class ChangeLog(Base):
    __tablename__ = 'workflow_change_logs'

    id = Column(Integer, primary_key=True, index=True)
    workflow_instance_id = Column(
        Integer, ForeignKey('workflow_instances.id'), nullable=False
    )
    changed_by = Column(String(255), nullable=False)
    change_type = Column(String(50), nullable=False)
    change_details = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    workflow_instance = relationship('WorkflowInstance')


class ApprovalRecord(Base):
    __tablename__ = 'workflow_approval_records'

    id = Column(Integer, primary_key=True, index=True)
    node_instance_id = Column(
        Integer, ForeignKey('workflow_node_instances.id'), nullable=False
    )
    approved_by = Column(String(255), nullable=False)
    approval_status = Column(String(50), nullable=False)
    comments = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    node_instance = relationship('NodeInstance')


class MachineTypeDuration(Base):
    __tablename__ = 'workflow_machine_type_durations'

    id = Column(Integer, primary_key=True, index=True)
    node_definition_id = Column(
        Integer,
        ForeignKey('workflow_node_definitions.id'),
        nullable=False,
        index=True,
    )
    machine_type = Column(String(50), nullable=False)
    expected_duration = Column(Integer, comment='Expected duration in minutes')
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(String(255))
    device_type = Column(
        Enum(DeviceType), nullable=False, default=DeviceType.main_machine
    )

    node_definition = relationship(
        'NodeDefinition', back_populates='machine_durations'
    )

    __table_args__ = (
        UniqueConstraint(
            'node_definition_id',
            'machine_type',
            'device_type',
            name='uq_node_machine_type',
        ),
    )


class SimulationData(Base):
    """仿真数据表"""

    __tablename__ = 'workflow_simulation_data'

    id = Column(Integer, primary_key=True, index=True)
    node_instance_id = Column(
        Integer,
        ForeignKey('workflow_node_instances.id'),
        nullable=False,
        comment='节点实例ID',
    )
    project_id = Column(String(100), nullable=False, comment='项目ID/ERP')
    simulation_efficiency = Column(
        String(255), nullable=True, comment='仿真效率(PPM)'
    )
    mechanical_issues = Column(String(255), default=0, comment='机械问题个数')
    program_issues = Column(String(255), default=0, comment='程序问题个数')
    remarks = Column(Text, nullable=True, comment='备注信息')
    # 新增仿真三维搭建相关字段
    simulation_3d_responsible = Column(String(100), nullable=True, comment='仿真搭建负责人')
    simulation_3d_start_time = Column(DateTime, nullable=True, comment='搭建开始时间')
    simulation_3d_end_time = Column(DateTime, nullable=True, comment='搭建结束时间')
    created_by = Column(String(100), nullable=True, comment='创建人')
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), comment='创建时间'
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        comment='更新时间',
    )


class InspectionTemplate(Base):
    """点检表模板表"""

    __tablename__ = 'inspection_templates'

    id = Column(Integer, primary_key=True, index=True)
    type = Column(String(100), nullable=False, comment='点检表类型')
    sequence_number = Column(Integer, nullable=False, comment='序号')
    inspection_item = Column(String(255), nullable=False, comment='点检项')
    area_function = Column(String(255), nullable=True, comment='区域/功能')
    category = Column(String(255), nullable=True, comment='类别')
    specific_plan = Column(Text, nullable=True, comment='具体方案或接线点检')
    inspection_method = Column(String(255), nullable=True, comment='检查方式')
    is_active = Column(Boolean, nullable=False, default=True, comment='是否启用')
    created_by = Column(String(100), nullable=True, comment='创建人')
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), comment='创建时间'
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        comment='更新时间',
    )

    # 关联关系
    project_inspections = relationship(
        'ProjectInspection',
        back_populates='template',
        cascade='all, delete-orphan',
    )

    __table_args__ = (
        UniqueConstraint(
            'type', 'sequence_number', name='uq_inspection_template_type_seq'
        ),
    )


class ProjectInspection(Base):
    """项目点检表实例表"""

    __tablename__ = 'project_inspections'

    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(String(100), nullable=False, comment='项目ID/ERP')
    template_id = Column(
        Integer,
        ForeignKey('inspection_templates.id'),
        nullable=False,
        comment='模板ID',
    )
    type = Column(String(100), nullable=False, comment='点检表类型')
    sequence_number = Column(Integer, nullable=False, comment='序号')
    inspection_item = Column(String(255), nullable=False, comment='点检项')
    area_function = Column(String(255), nullable=True, comment='区域/功能')
    category = Column(String(255), nullable=True, comment='类别')
    specific_plan = Column(Text, nullable=True, comment='具体方案或接线点检')
    inspection_method = Column(String(255), nullable=True, comment='检查方式')
    self_check_result = Column(String(255), nullable=True, comment='自查结论')
    self_checker = Column(String(100), nullable=True, comment='自检人')
    audit_result = Column(String(255), nullable=True, comment='审核结论')
    auditor = Column(String(100), nullable=True, comment='审核人')
    is_completed = Column(
        Boolean, nullable=False, default=False, comment='是否完成'
    )
    completed_at = Column(DateTime, nullable=True, comment='完成时间')
    created_by = Column(String(100), nullable=True, comment='创建人')
    created_at = Column(
        DateTime(timezone=True), server_default=func.now(), comment='创建时间'
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        comment='更新时间',
    )

    # 关联关系
    template = relationship(
        'InspectionTemplate', back_populates='project_inspections'
    )

    __table_args__ = (
        UniqueConstraint(
            'project_id',
            'template_id',
            name='uq_project_inspection_project_template',
        ),
    )
