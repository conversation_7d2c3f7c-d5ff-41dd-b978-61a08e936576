from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional
from .approval import SimulationDataBase


class SimulationDataCreate(SimulationDataBase):
    """创建仿真数据模型"""

    node_instance_id: int = Field(..., description='节点实例ID')
    created_by: Optional[str] = Field(None, description='创建人')
    project_id: str = Field(..., description='项目ID')


class SimulationDataUpdate(BaseModel):
    """更新仿真数据模型"""

    simulation_efficiency: Optional[str] = Field(None, description='仿真效率(PPM)')
    mechanical_issues: Optional[str] = Field(None, description='机械问题个数')
    program_issues: Optional[str] = Field(None, description='程序问题个数')
    remarks: Optional[str] = Field(None, description='备注信息')
    # 新增仿真三维搭建相关字段
    simulation_3d_responsible: Optional[str] = Field(
        None, description='仿真搭建负责人'
    )
    simulation_3d_start_time: Optional[datetime] = Field(
        None, description='搭建开始时间'
    )
    simulation_3d_end_time: Optional[datetime] = Field(
        None, description='搭建结束时间'
    )


class SimulationDataResponse(SimulationDataBase):
    """仿真数据响应模型"""

    id: int
    node_instance_id: int
    created_by: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class Simulation3DSubmitRequest(BaseModel):
    """仿真三维搭建提交请求模型"""

    simulation_3d_responsible: str = Field(..., description='仿真搭建负责人')
    simulation_3d_start_time: datetime = Field(..., description='搭建开始时间')
    simulation_3d_end_time: datetime = Field(..., description='搭建结束时间')


class SimulationApprovalRequest(BaseModel):
    """仿真节点审批请求模型"""

    # 审批信息
    approval_status: str = Field(..., description='审批状态（APPROVED/REJECTED）')
    comments: Optional[str] = Field(None, description='审批意见')
    approved_by: str = Field(..., description='审批人ID')

    # 仿真数据（可选）
    simulation_data: Optional[SimulationDataBase] = Field(
        None, description='仿真数据'
    )
